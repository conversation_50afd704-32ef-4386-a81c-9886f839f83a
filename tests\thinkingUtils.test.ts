import { describe, it, expect } from 'vitest';
import { processThinkingResponse, formatThinkingContent } from '../src/lib/utils/thinkingUtils';

describe('Thinking Utils', () => {
  describe('processThinkingResponse', () => {
    it('should handle response with no thinking tags', () => {
      const response = "  Hello world  ";
      const result = processThinkingResponse(response);

      expect(result.content).toBe("  Hello world  ");
      expect(result.thinking).toBe(null);
      expect(result.hasThinking).toBe(false);
    });

    it('should preserve spacing in thinking tags', () => {
      const response = "<think>Some thinking</think>  Hello world  ";
      const result = processThinkingResponse(response);

      expect(result.content).toBe("  Hello world  ");
      expect(result.thinking).toBe("Some thinking");
      expect(result.hasThinking).toBe(true);
    });

    it('should handle multiple thinking blocks', () => {
      const response = "<think>First thought</think>  Content with spaces  <think>Second thought</think>";
      const result = processThinkingResponse(response);

      expect(result.content).toBe("  Content with spaces  ");
      expect(result.thinking).toBe("First thought\n\n---\n\nSecond thought");
      expect(result.hasThinking).toBe(true);
    });

    it('should preserve leading and trailing spaces', () => {
      const response = "<think>Thinking</think>   Leading and trailing spaces   ";
      const result = processThinkingResponse(response);

      expect(result.content).toBe("   Leading and trailing spaces   ");
      expect(result.thinking).toBe("Thinking");
      expect(result.hasThinking).toBe(true);
    });
  });

  describe('formatThinkingContent', () => {
    it('should format thinking content', () => {
      const thinking = "First paragraph\n\nSecond paragraph";
      const result = formatThinkingContent(thinking);

      expect(result).toBe("First paragraph\n\nSecond paragraph");
    });
  });
});
