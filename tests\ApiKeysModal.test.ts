import { describe, it, expect, beforeEach, vi } from 'vitest';
import { get } from 'svelte/store';
import { apiKeys, updateApiKey, initializeAllSettings } from '$lib/stores/unifiedSettingsStore';
import { DEFAULT_API_KEYS } from '$lib/config';

// Mock the storage service
vi.mock('$lib/services/storageService', () => ({
	loadUnifiedSettings: vi.fn(),
	saveUnifiedSettings: vi.fn(),
	UNIFIED_SETTINGS_STORAGE_KEY: 'aiNotepadSvelteUnifiedSettings'
}));

import { saveUnifiedSettings, loadUnifiedSettings } from '$lib/services/storageService';

describe('API Keys Modal Integration', () => {
	beforeEach(() => {
		// Reset mocks and localStorage
		vi.clearAllMocks();
		localStorage.clear();

		// Reset store to default state
		apiKeys.set({ ...DEFAULT_API_KEYS });

		// Mock saveUnifiedSettings to return true
		vi.mocked(saveUnifiedSettings).mockReturnValue(true);
	});

	it('should initialize with default API keys', () => {
		const keys = get(apiKeys);
		expect(keys).toEqual(DEFAULT_API_KEYS);
		expect(keys.openai).toBe('');
		expect(keys.gemini).toBe('');
		expect(keys.customUrl).toBe('');
		expect(keys.customKey).toBe('');
	});

	it('should update OpenAI API key', () => {
		updateApiKey('openai', 'sk-test-openai-key');

		const keys = get(apiKeys);
		expect(keys.openai).toBe('sk-test-openai-key');
		expect(keys.gemini).toBe(''); // Should remain unchanged
	});

	it('should update Gemini API key', () => {
		updateApiKey('gemini', 'gemini-test-key');

		const keys = get(apiKeys);
		expect(keys.gemini).toBe('gemini-test-key');
		expect(keys.openai).toBe(''); // Should remain unchanged
	});

	it('should update custom URL', () => {
		updateApiKey('customUrl', 'https://api.custom.com/v1');

		const keys = get(apiKeys);
		expect(keys.customUrl).toBe('https://api.custom.com/v1');
	});

	it('should update custom API key', () => {
		updateApiKey('customKey', 'custom-api-key');

		const keys = get(apiKeys);
		expect(keys.customKey).toBe('custom-api-key');
	});

	it('should handle multiple API key updates independently', () => {
		// Update multiple keys
		updateApiKey('openai', 'sk-openai-key');
		updateApiKey('gemini', 'gemini-key');
		updateApiKey('customUrl', 'https://api.custom.com');
		updateApiKey('customKey', 'custom-key');

		const keys = get(apiKeys);
		expect(keys).toEqual({
			openai: 'sk-openai-key',
			gemini: 'gemini-key',
			customUrl: 'https://api.custom.com',
			customKey: 'custom-key'
		});
	});

	it('should handle empty string values', () => {
		// First set a value
		updateApiKey('openai', 'sk-test-key');
		expect(get(apiKeys).openai).toBe('sk-test-key');

		// Then clear it
		updateApiKey('openai', '');
		expect(get(apiKeys).openai).toBe('');
	});

	it('should preserve other keys when updating one key', () => {
		// Set initial values
		updateApiKey('openai', 'sk-openai-key');
		updateApiKey('gemini', 'gemini-key');

		// Update only one key
		updateApiKey('openai', 'sk-new-openai-key');

		const keys = get(apiKeys);
		expect(keys.openai).toBe('sk-new-openai-key');
		expect(keys.gemini).toBe('gemini-key'); // Should be preserved
		expect(keys.customUrl).toBe(''); // Should remain default
		expect(keys.customKey).toBe(''); // Should remain default
	});

	it('should support persistence simulation (refresh test)', async () => {
		// Simulate setting API keys
		const testApiKeys = {
			openai: 'sk-persistent-key',
			gemini: 'persistent-gemini-key',
			customUrl: 'https://persistent.api.com',
			customKey: 'persistent-custom-key'
		};

		// Set each key
		updateApiKey('openai', testApiKeys.openai);
		updateApiKey('gemini', testApiKeys.gemini);
		updateApiKey('customUrl', testApiKeys.customUrl);
		updateApiKey('customKey', testApiKeys.customKey);

		// Verify all keys are set correctly
		const keys = get(apiKeys);
		expect(keys).toEqual(testApiKeys);

		// Simulate page refresh by mocking loadUnifiedSettings to return the saved data
		// and reinitializing the store
		vi.mocked(loadUnifiedSettings).mockReturnValue({
			apiKeys: testApiKeys,
			appSettings: {
				fontFamily: 'Arial, sans-serif',
				fontSize: '16',
				aiProvider: 'openai',
				aiModel: 'gpt-4o-mini',
				autocompleteContextLength: 1000
			},
			notepadContent: '',
			customModelHistory: { gemini: [], openai: [], custom: [] },
			lastCustomModels: { gemini: '', openai: '', custom: '' },
			lastSelectedModels: {
				gemini: 'gemini-1.5-flash',
				openai: 'gpt-4o-mini',
				custom: ''
			},
			providerSpecificModels: {
				gemini: 'gemini-1.5-flash',
				openai: 'gpt-4o-mini',
				custom: ''
			}
		});

		// Simulate refresh by reinitializing
		await initializeAllSettings();

		// Verify keys are still there after "refresh"
		const keysAfterRefresh = get(apiKeys);
		expect(keysAfterRefresh).toEqual(testApiKeys);
	});
});
