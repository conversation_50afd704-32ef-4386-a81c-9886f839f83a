<script lang="ts">
  import ApiKeysModal from './ApiKeysModal.svelte';
  import DarkModeToggle from './DarkModeToggle.svelte';

  // Modal state management
  let showApiKeysModal = false;

  const openApiKeysModal = () => showApiKeysModal = true;
  const closeApiKeysModal = () => showApiKeysModal = false;
</script>

<!-- Application header with logo and controls -->
<header class="app-header">
  <div class="header-logo">AI<span>Pad</span></div>
  <div class="api-controls">
    <DarkModeToggle />
    <button class="api-keys-button" on:click={openApiKeysModal}>
      🔑 API Keys
    </button>
  </div>
</header>

<!-- API Keys configuration modal -->
<ApiKeysModal bind:isOpen={showApiKeysModal} onclose={closeApiKeysModal} />

<style>
  .app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background-color: var(--color-header-bg);
    color: white;
    flex-shrink: 0;
    transition: background-color 0.2s ease;
  }
  .header-logo {
    font-size: 1.5em;
    font-weight: bold;
  }
  .header-logo span {
    color: var(--color-accent);
  }
  .api-controls {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  .api-keys-button {
    padding: 8px 16px;
    background-color: var(--color-accent);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: background-color 0.2s;
  }
  .api-keys-button:hover {
    background-color: #45a049;
  }
</style>