import { describe, it, expect, beforeEach, vi } from 'vitest';
import { get } from 'svelte/store';
import {
    appSettings,
    updateAppSetting,
    initializeAllSettings,
    settingsLoaded
} from '$lib/stores/unifiedSettingsStore';
import { DEFAULT_APP_SETTINGS } from '$lib/config';

// Mock the API service
vi.mock('$lib/services/apiService', () => ({
    callAIWithThinking: vi.fn()
}));

// Mock the storage service
vi.mock('$lib/services/storageService', () => ({
    loadUnifiedSettings: vi.fn(() => ({
        apiKeys: { openai: '', gemini: '', customUrl: '', customKey: '' },
        appSettings: { ...DEFAULT_APP_SETTINGS },
        notepadContent: '',
        customModelHistory: { gemini: [], openai: [], custom: [] },
        lastCustomModels: { gemini: '', openai: '', custom: '' },
        lastSelectedModels: { gemini: 'gemini-1.5-pro', openai: 'gpt-4o-mini', custom: '' },
        providerSpecificModels: { gemini: 'gemini-1.5-pro', openai: 'gpt-4o-mini', custom: '' }
    })),
    saveUnifiedSettings: vi.fn(),
    UNIFIED_SETTINGS_STORAGE_KEY: 'aiNotepadSvelteUnifiedSettings'
}));

// Mock status store
vi.mock('$lib/stores/statusStore', () => ({
    showStatus: vi.fn()
}));

describe('Notepad Thinking Panel Toggle Integration', () => {
    beforeEach(async () => {
        // Reset mocks
        vi.clearAllMocks();

        // Reset the store before each test
        appSettings.set({ ...DEFAULT_APP_SETTINGS, showThinkingPanel: true });
        settingsLoaded.set(true);

        await initializeAllSettings();
        await new Promise(resolve => setTimeout(resolve, 100));
    });

    it('should not show thinking toggle button when setting is disabled', async () => {
        // Disable thinking panel
        updateAppSetting('showThinkingPanel', false);

        const settings = get(appSettings);
        expect(settings.showThinkingPanel).toBe(false);

        // In the new design, thinking button only shows when:
        // 1. showThinkingPanel is true AND 2. lastThinking exists
        // Since showThinkingPanel is false, button should not show regardless of thinking content
    });

    it('should show thinking toggle button when setting is enabled and thinking content exists', async () => {
        // Enable thinking panel
        updateAppSetting('showThinkingPanel', true);

        const settings = get(appSettings);
        expect(settings.showThinkingPanel).toBe(true);

        // In the new design, thinking button shows when:
        // 1. showThinkingPanel is true AND 2. lastThinking exists
        // This test verifies the setting is enabled (condition 1)
        // The actual thinking content (condition 2) would come from AI responses
    });

    it('should not show thinking panel when setting is disabled', async () => {
        // Disable thinking panel
        updateAppSetting('showThinkingPanel', false);

        const settings = get(appSettings);
        expect(settings.showThinkingPanel).toBe(false);

        // In the new design, thinking panel shows when:
        // showThinkingPanel && lastThinking && showThinkingPanel (UI state)
        // Since showThinkingPanel setting is false, panel should not show
    });

    it('should hide thinking panel when setting is toggled off', async () => {
        // Start with thinking panel enabled
        updateAppSetting('showThinkingPanel', true);
        expect(get(appSettings).showThinkingPanel).toBe(true);

        // Disable thinking panel
        updateAppSetting('showThinkingPanel', false);
        expect(get(appSettings).showThinkingPanel).toBe(false);

        // The component logic: if (!$appSettings.showThinkingPanel) { showThinkingPanel = false; }
        // This ensures the UI panel is hidden when the setting is disabled
    });

    it('should respect thinking panel setting for text selection behavior', async () => {
        // Enable thinking panel
        updateAppSetting('showThinkingPanel', true);
        expect(get(appSettings).showThinkingPanel).toBe(true);

        // In the new design, text selection no longer affects thinking display
        // The thinking content is based only on the last AI response (lastThinking)
        // This is a significant simplification from the previous Map-based approach
    });

    it('should maintain textarea functionality regardless of thinking panel setting', async () => {
        // Test with thinking panel enabled
        updateAppSetting('showThinkingPanel', true);
        expect(get(appSettings).showThinkingPanel).toBe(true);

        // Test with thinking panel disabled
        updateAppSetting('showThinkingPanel', false);
        expect(get(appSettings).showThinkingPanel).toBe(false);

        // The textarea functionality is independent of thinking panel settings
        // This test verifies that the setting changes work correctly
    });

    it('should show AI action buttons regardless of thinking panel setting', async () => {
        // Check with thinking panel enabled
        updateAppSetting('showThinkingPanel', true);
        expect(get(appSettings).showThinkingPanel).toBe(true);

        // Check with thinking panel disabled
        updateAppSetting('showThinkingPanel', false);
        expect(get(appSettings).showThinkingPanel).toBe(false);

        // AI action buttons (Autocomplete, Replace Selection, Insert at Cursor)
        // are always present regardless of thinking panel setting
        // Only the thinking toggle button visibility depends on the setting
    });
});
