<script lang="ts">
  import { onMount } from 'svelte';
  import { browser } from '$app/environment';
  import type { ModelConfig } from '../config';

  // Component props
  export let models: ModelConfig[] = [];
  export let value: string = '';
  export let placeholder: string = 'Select a model';
  export let onchange: ((modelId: string) => void) | undefined = undefined;

  // Component state
  let isOpen = false;
  let selectElement: HTMLDivElement;

  // Get the currently selected model
  $: selectedModel = models.find(model => model.id === value);

  // Handle model selection
  const selectModel = (modelId: string) => {
    value = modelId;
    isOpen = false;
    onchange?.(modelId);
  };

  // Toggle dropdown visibility
  const toggleDropdown = () => {
    isOpen = !isOpen;
  };

  // Handle keyboard navigation
  const handleKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      isOpen = false;
    } else if (event.key === 'ArrowDown' && !isOpen) {
      isOpen = true;
    } else if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      toggleDropdown();
    }
  };

  // Close dropdown when clicking outside
  const handleClickOutside = (event: MouseEvent) => {
    if (selectElement && !selectElement.contains(event.target as Node)) {
      isOpen = false;
    }
  };

  // Set up click outside listener
  onMount(() => {
    if (browser) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  });
</script>

<!-- Custom model select with enhanced display -->
<div class="model-select" bind:this={selectElement}>
  <!-- Select trigger -->
  <div 
    class="select-trigger"
    class:open={isOpen}
    role="button"
    tabindex="0"
    aria-haspopup="listbox"
    aria-expanded={isOpen}
    on:click={toggleDropdown}
    on:keydown={handleKeydown}
  >
    <div class="selected-content">
      {#if selectedModel}
        <div class="model-name">{selectedModel.name}</div>
        {#if selectedModel.description}
          <div class="model-description">{selectedModel.description}</div>
        {/if}
      {:else}
        <div class="placeholder">{placeholder}</div>
      {/if}
    </div>
    <div class="dropdown-arrow" class:open={isOpen}>▼</div>
  </div>

  <!-- Dropdown with model options -->
  {#if isOpen}
    <div class="dropdown" role="listbox">
      {#each models as model (model.id)}
        <div
          class="dropdown-item"
          class:selected={model.id === value}
          role="option"
          aria-selected={model.id === value}
          tabindex="0"
          on:click={() => selectModel(model.id)}
          on:keydown={(e) => e.key === 'Enter' && selectModel(model.id)}
        >
          <div class="model-name">{model.name}</div>
          {#if model.description}
            <div class="model-description">{model.description}</div>
          {/if}
        </div>
      {/each}
    </div>
  {/if}
</div>

<style>
  .model-select {
    position: relative;
    width: 100%;
  }

  .select-trigger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border: 1px solid var(--color-border);
    border-radius: 3px;
    background-color: var(--color-input-bg);
    color: var(--color-text-primary);
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 40px;
  }

  .select-trigger:hover {
    border-color: var(--color-accent);
  }

  .select-trigger:focus {
    outline: none;
    border-color: var(--color-accent);
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.25);
  }

  .select-trigger.open {
    border-color: var(--color-accent);
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }

  .selected-content {
    flex: 1;
    text-align: left;
  }

  .model-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--color-text-primary);
    line-height: 1.2;
  }

  .model-description {
    font-size: 12px;
    color: var(--color-text-primary);
    opacity: 0.7;
    margin-top: 2px;
    line-height: 1.2;
  }

  .placeholder {
    font-size: 14px;
    color: var(--color-text-primary);
    opacity: 0.6;
  }

  .dropdown-arrow {
    font-size: 12px;
    color: var(--color-text-primary);
    opacity: 0.7;
    transition: transform 0.2s ease;
    margin-left: 8px;
  }

  .dropdown-arrow.open {
    transform: rotate(180deg);
  }

  .dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--color-modal-bg);
    border: 1px solid var(--color-border);
    border-top: none;
    border-radius: 0 0 3px 3px;
    box-shadow: 0 2px 8px var(--color-shadow);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
  }

  .dropdown-item {
    padding: 10px 12px;
    cursor: pointer;
    border-bottom: 1px solid var(--color-border);
    transition: all 0.2s ease;
  }

  .dropdown-item:last-child {
    border-bottom: none;
  }

  .dropdown-item:hover {
    background-color: var(--color-toggle-hover);
  }

  .dropdown-item.selected {
    background-color: var(--color-accent);
    color: white;
  }

  .dropdown-item.selected .model-description {
    color: white;
    opacity: 0.9;
  }

  .dropdown-item:focus {
    outline: none;
    background-color: var(--color-toggle-hover);
  }

  .dropdown-item.selected:focus {
    background-color: var(--color-accent);
  }
</style>
