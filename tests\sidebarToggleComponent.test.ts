import { describe, it, expect, beforeEach, vi } from 'vitest';
import { get } from 'svelte/store';
import { appSettings, updateAppSetting } from '$lib/stores/unifiedSettingsStore';
import { DEFAULT_APP_SETTINGS } from '$lib/config';

// Mock the storage service
vi.mock('$lib/services/storageService', () => ({
    loadUnifiedSettings: vi.fn(() => ({
        apiKeys: { openai: '', gemini: '', customUrl: '', customKey: '' },
        appSettings: { ...DEFAULT_APP_SETTINGS },
        notepadContent: '',
        customModelHistory: { gemini: [], openai: [], custom: [] },
        lastCustomModels: { gemini: '', openai: '', custom: '' },
        lastSelectedModels: { gemini: 'gemini-1.5-pro', openai: 'gpt-4o-mini', custom: '' },
        providerSpecificModels: { gemini: 'gemini-1.5-pro', openai: 'gpt-4o-mini', custom: '' }
    })),
    saveUnifiedSettings: vi.fn(),
    UNIFIED_SETTINGS_STORAGE_KEY: 'aiNotepadSvelteUnifiedSettings'
}));

describe('Sidebar Toggle Component Logic', () => {
    beforeEach(() => {
        // Reset the store before each test
        appSettings.set({ ...DEFAULT_APP_SETTINGS });
    });

    it('should have correct initial state', () => {
        const settings = get(appSettings);
        expect(settings.sidebarVisible).toBe(true);
    });

    it('should toggle correctly', () => {
        // Test toggle from true to false
        updateAppSetting('sidebarVisible', false);
        expect(get(appSettings).sidebarVisible).toBe(false);
        
        // Test toggle from false to true
        updateAppSetting('sidebarVisible', true);
        expect(get(appSettings).sidebarVisible).toBe(true);
    });

    it('should maintain state consistency', () => {
        // Change state multiple times
        updateAppSetting('sidebarVisible', false);
        updateAppSetting('sidebarVisible', true);
        updateAppSetting('sidebarVisible', false);
        
        expect(get(appSettings).sidebarVisible).toBe(false);
    });

    it('should work with other settings', () => {
        // Change sidebar visibility
        updateAppSetting('sidebarVisible', false);
        
        // Change other settings
        updateAppSetting('fontSize', '18');
        updateAppSetting('fontFamily', 'Georgia, serif');
        
        // Sidebar visibility should remain unchanged
        expect(get(appSettings).sidebarVisible).toBe(false);
        expect(get(appSettings).fontSize).toBe('18');
        expect(get(appSettings).fontFamily).toBe('Georgia, serif');
    });
});
