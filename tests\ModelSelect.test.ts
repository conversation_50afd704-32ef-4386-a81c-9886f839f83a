import { describe, it, expect } from 'vitest';
import type { ModelConfig } from '../src/lib/config';

describe('ModelSelect', () => {
  const mockModels: ModelConfig[] = [
    { id: 'gpt-4o', name: 'GPT-4o', description: 'Flagship, Intelligent, Multimodal' },
    { id: 'gpt-4o-mini', name: 'GPT-4o mini', description: 'Fast, Cost-Effective, Multimodal' },
    { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash', description: 'Fast, Efficient, Good Value' },
    { id: 'simple-model', name: 'Simple Model', description: '' } // Model without description
  ];

  it('validates model structure with separate name and description fields', () => {
    const model = mockModels[0];
    expect(model.id).toBe('gpt-4o');
    expect(model.name).toBe('GPT-4o');
    expect(model.description).toBe('Flagship, Intelligent, Multimodal');
  });

  it('handles models with empty descriptions', () => {
    const model = mockModels[3];
    expect(model.id).toBe('simple-model');
    expect(model.name).toBe('Simple Model');
    expect(model.description).toBe('');
  });

  it('validates all mock models have required fields', () => {
    mockModels.forEach(model => {
      expect(model).toHaveProperty('id');
      expect(model).toHaveProperty('name');
      expect(model).toHaveProperty('description');
      expect(typeof model.id).toBe('string');
      expect(typeof model.name).toBe('string');
      expect(typeof model.description).toBe('string');
    });
  });

  it('ensures model IDs are unique', () => {
    const ids = mockModels.map(model => model.id);
    const uniqueIds = new Set(ids);
    expect(uniqueIds.size).toBe(ids.length);
  });

  it('validates model names are not empty', () => {
    mockModels.forEach(model => {
      expect(model.name.trim()).not.toBe('');
    });
  });
});
