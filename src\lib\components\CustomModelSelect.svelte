<script lang="ts">
  import { onMount } from 'svelte';
  import { customModelHistory, removeModelFromHistory, appSettings } from '../stores/unifiedSettingsStore';
  import type { ModelHistoryItem } from '../types';

  // Component props
  let {
    value = $bindable(''),
    placeholder = 'Enter custom model name',
    oninput,
    onchange
  }: {
    value?: string;
    placeholder?: string;
    oninput?: (value: string) => void;
    onchange?: (value: string) => void;
  } = $props();

  // Component state
  let isOpen = $state(false);
  let inputElement: HTMLInputElement;
  let dropdownElement: HTMLDivElement;

  // Get history for current provider and filter based on input
  const currentProviderHistory = $derived($customModelHistory[$appSettings.aiProvider] || []);
  const filteredHistory = $derived(value.trim() === ''
    ? currentProviderHistory
    : currentProviderHistory.filter(item =>
        item.model.toLowerCase().includes(value.toLowerCase())
      ));

  // Handle input changes
  const handleInput = (event: Event) => {
    const target = event.target as HTMLInputElement;
    value = target.value;
    oninput?.(value);

    // Auto-open dropdown when typing
    if (!isOpen && value.length > 0) {
      isOpen = true;
    }
  };

  // Handle input change completion
  const handleChange = () => onchange?.(value);

  // Select a model from history
  const selectModel = (model: string) => {
    value = model;
    isOpen = false;
    oninput?.(value);
    onchange?.(value);
    inputElement.focus();
  };

  // Remove model from history
  const removeModel = (event: Event, model: string) => {
    event.stopPropagation();
    removeModelFromHistory($appSettings.aiProvider, model);
  };

  // Toggle dropdown visibility
  const toggleDropdown = () => {
    isOpen = !isOpen;
    if (isOpen) inputElement.focus();
  };

  // Handle keyboard navigation
  const handleKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      isOpen = false;
    } else if (event.key === 'ArrowDown' && !isOpen) {
      isOpen = true;
    }
  };

  // Close dropdown when clicking outside
  const handleClickOutside = (event: MouseEvent) => {
    if (dropdownElement && !dropdownElement.contains(event.target as Node)) {
      isOpen = false;
    }
  };

  // Set up click outside listener
  onMount(() => {
    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  });
</script>

<!-- Custom model select with history dropdown -->
<div class="custom-model-select" bind:this={dropdownElement}>
  <!-- Input field with dropdown arrow -->
  <div class="input-container">
    <input
      bind:this={inputElement}
      type="text"
      {placeholder}
      {value}
      oninput={handleInput}
      onchange={handleChange}
      onkeydown={handleKeydown}
      onfocus={() => isOpen = true}
      class="model-input"
    />
    <button
      type="button"
      class="dropdown-arrow"
      class:open={isOpen}
      onclick={toggleDropdown}
      aria-label="Toggle dropdown"
    >
      ▼
    </button>
  </div>

  <!-- Dropdown with history and new model options -->
  {#if isOpen && (filteredHistory.length > 0 || value.trim() !== '')}
    <div class="dropdown">
      <!-- Recent models section -->
      {#if filteredHistory.length > 0}
        <div class="dropdown-section">
          <div class="section-header">Recent Models</div>
          {#each filteredHistory as item (item.model)}
            <div
              class="dropdown-item"
              role="option"
              aria-selected="false"
              tabindex="0"
              onclick={() => selectModel(item.model)}
              onkeydown={(e) => e.key === 'Enter' && selectModel(item.model)}
            >
              <span class="model-name">{item.model}</span>
              <button
                type="button"
                class="remove-button"
                onclick={(e) => removeModel(e, item.model)}
                aria-label="Remove from history"
              >
                ×
              </button>
            </div>
          {/each}
        </div>
      {/if}

      <!-- New model option (if input doesn't match existing) -->
      {#if value.trim() !== '' && !filteredHistory.some(item => item.model === value.trim())}
        <div class="dropdown-section">
          <div class="section-header">New Model</div>
          <div
            class="dropdown-item new-model"
            role="option"
            aria-selected="false"
            tabindex="0"
            onclick={() => selectModel(value.trim())}
            onkeydown={(e) => e.key === 'Enter' && selectModel(value.trim())}
          >
            <span class="model-name">Use "{value.trim()}"</span>
          </div>
        </div>
      {/if}
    </div>
  {/if}
</div>

<style>
  .custom-model-select {
    position: relative;
    width: 100%;
  }

  .input-container {
    position: relative;
    display: flex;
    align-items: center;
  }

  .model-input {
    width: 100%;
    padding: 8px 32px 8px 8px;
    box-sizing: border-box;
    border: 1px solid var(--color-border);
    border-radius: 3px;
    font-size: 14px;
    background-color: var(--color-input-bg);
    color: var(--color-text-primary);
    transition: all 0.2s ease;
  }

  .model-input:focus {
    outline: none;
    border-color: var(--color-accent);
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.25);
  }

  .dropdown-arrow {
    position: absolute;
    right: 8px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 12px;
    color: var(--color-text-primary);
    opacity: 0.7;
    padding: 4px;
    transition: all 0.2s ease;
  }

  .dropdown-arrow:hover {
    opacity: 1;
  }

  .dropdown-arrow.open {
    transform: rotate(180deg);
  }

  .dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--color-modal-bg);
    border: 1px solid var(--color-border);
    border-top: none;
    border-radius: 0 0 3px 3px;
    box-shadow: 0 2px 8px var(--color-shadow);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
    transition: all 0.2s ease;
  }

  .dropdown-section {
    border-bottom: 1px solid var(--color-border);
  }

  .dropdown-section:last-child {
    border-bottom: none;
  }

  .section-header {
    padding: 8px 12px 4px;
    font-size: 11px;
    font-weight: bold;
    color: var(--color-text-primary);
    opacity: 0.7;
    text-transform: uppercase;
    background-color: var(--color-modal-header);
    transition: all 0.2s ease;
  }

  .dropdown-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid var(--color-border);
    transition: all 0.2s ease;
  }

  .dropdown-item:last-child {
    border-bottom: none;
  }

  .dropdown-item:hover {
    background-color: var(--color-toggle-hover);
  }

  .dropdown-item.new-model {
    background-color: var(--color-ai-generated);
  }

  .dropdown-item.new-model:hover {
    background-color: var(--color-info);
  }

  .model-name {
    flex: 1;
    font-size: 14px;
    color: var(--color-text-primary);
  }

  .remove-button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
    color: var(--color-text-primary);
    opacity: 0.5;
    padding: 2px 6px;
    margin-left: 8px;
    border-radius: 2px;
    line-height: 1;
    transition: all 0.2s ease;
  }

  .remove-button:hover {
    background-color: var(--color-error);
    color: var(--color-text-primary);
    opacity: 1;
  }
</style>
