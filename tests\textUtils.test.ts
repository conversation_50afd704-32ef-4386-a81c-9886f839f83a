import { describe, it, expect } from 'vitest';
import { getTextContext, extractSelectionContext, createAutocompletePrompt, createAnswerSelectionPrompt, extractCursorContext, createCursorInsertPrompt } from '../src/lib/utils/textUtils';

describe('Text Utils', () => {
  describe('getTextContext', () => {
    it('should return full text when no max length specified', () => {
      const text = "Hello world";
      const result = getTextContext(text);
      expect(result).toBe("Hello world");
    });

    it('should truncate text when max length specified', () => {
      const text = "Hello world this is a long text";
      const result = getTextContext(text, 10);
      expect(result).toBe(" long text");
    });

    it('should return full text when max length is longer than text', () => {
      const text = "Short";
      const result = getTextContext(text, 100);
      expect(result).toBe("Short");
    });
  });

  describe('extractSelectionContext', () => {
    it('should extract context around selection - basic case', () => {
      const fullText = "The quick brown fox jumps over the lazy dog";
      const result = extractSelectionContext(fullText, 10, 15, 5, 5);

      expect(result.selectedText).toBe("brown");
      expect(result.beforeText).toBe("uick ");
      expect(result.afterText).toBe(" fox ");
    });

    it('should extract context around selection - at beginning', () => {
      const fullText = "Hello world";
      const result = extractSelectionContext(fullText, 0, 5, 10, 3);

      expect(result.selectedText).toBe("Hello");
      expect(result.beforeText).toBe("");
      expect(result.afterText).toBe(" wo");
    });

    it('should extract context around selection - at end', () => {
      const fullText = "Hello world";
      const result = extractSelectionContext(fullText, 6, 11, 3, 10);

      expect(result.selectedText).toBe("world");
      expect(result.beforeText).toBe("lo ");
      expect(result.afterText).toBe("");
    });
  });

  describe('Prompt Creation', () => {
    it('should create proper autocomplete prompt', () => {
      const context = "The weather is";
      const systemPrompt = "Continue writing the following text";
      const result = createAutocompletePrompt(context, systemPrompt);

      expect(result).toContain(context);
      expect(result).toContain(systemPrompt);
    });

    it('should create proper answer selection prompt', () => {
      const beforeText = "The cat sat";
      const afterText = "and meowed loudly";
      const systemPrompt = "Provide text that would naturally fit between segments";
      const result = createAnswerSelectionPrompt(beforeText, afterText, systemPrompt);

      expect(result).toContain(beforeText);
      expect(result).toContain(afterText);
      expect(result).toContain(systemPrompt);
    });

    it('should include whitespace preservation instructions in autocomplete prompt', () => {
      const context = "The weather is ";
      const systemPrompt = "Continue writing. Respect all whitespace including trailing spaces.";
      const result = createAutocompletePrompt(context, systemPrompt);

      expect(result).toContain("Respect all whitespace");
      expect(result).toContain("trailing spaces");
    });

    it('should include whitespace preservation instructions in answer selection prompt', () => {
      const beforeText = "The cat sat ";
      const afterText = " and meowed loudly";
      const systemPrompt = "Provide text. Respect all whitespace including trailing or leading spaces.";
      const result = createAnswerSelectionPrompt(beforeText, afterText, systemPrompt);

      expect(result).toContain("Respect all whitespace");
      expect(result).toContain("trailing or leading spaces");
    });
  });

  describe('extractCursorContext', () => {
    it('should extract context around cursor - basic case', () => {
      const fullText = "The quick brown fox jumps over the lazy dog";
      const result = extractCursorContext(fullText, 20, 10, 8);

      expect(result.beforeText).toBe("brown fox ");
      expect(result.afterText).toBe("jumps ov");
    });

    it('should extract context around cursor - at beginning', () => {
      const fullText = "Hello world";
      const result = extractCursorContext(fullText, 0, 10, 5);

      expect(result.beforeText).toBe("");
      expect(result.afterText).toBe("Hello");
    });

    it('should extract context around cursor - at end', () => {
      const fullText = "Hello world";
      const result = extractCursorContext(fullText, 11, 5, 10);

      expect(result.beforeText).toBe("world");
      expect(result.afterText).toBe("");
    });

  });

  describe('createCursorInsertPrompt', () => {
    it('should create proper cursor insert prompt', () => {
      const beforeText = "The cat";
      const afterText = "meowed loudly";
      const systemPrompt = "Provide text to insert at cursor position";
      const result = createCursorInsertPrompt(beforeText, afterText, systemPrompt);

      expect(result).toContain(beforeText);
      expect(result).toContain(afterText);
      expect(result).toContain("cursor");
      expect(result).toContain(systemPrompt);
    });

    it('should include whitespace preservation instructions in cursor insert prompt', () => {
      const beforeText = "The cat ";
      const afterText = " meowed loudly";
      const systemPrompt = "Provide text. Respect all whitespace including trailing or leading spaces.";
      const result = createCursorInsertPrompt(beforeText, afterText, systemPrompt);

      expect(result).toContain("Respect all whitespace");
      expect(result).toContain("trailing or leading spaces");
    });
  });
});
