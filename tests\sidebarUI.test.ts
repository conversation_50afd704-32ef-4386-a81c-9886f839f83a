import { describe, it, expect, beforeEach, vi } from 'vitest';
import { initializeAllSettings, switchAIProvider, updateModelSelection, appSettings } from '$lib/stores/unifiedSettingsStore';
import { get } from 'svelte/store';

// Mock the unifiedSettingsStore
vi.mock('$lib/stores/unifiedSettingsStore', async () => {
    const originalModule = await vi.importActual('$lib/stores/unifiedSettingsStore');
    return {
        ...originalModule,
        initializeAllSettings: vi.fn(),
    };
});

describe('Sidebar UI Behavior', () => {
    beforeEach(async () => {
        // Reset the store before each test
        appSettings.set({
            fontFamily: 'Arial, sans-serif',
            fontSize: '16',
            textAlign: 'left',
            lineSpacing: '1.5',
            aiProvider: 'openai',
            aiModel: 'gpt-4o-mini',
            autocompleteContextLength: 1000,
            temperature: 0.7,
            topP: 0.9,
            sidebarVisible: true,
            darkMode: false,
            showThinkingPanel: true,
            systemPrompts: {
                autocomplete: "Default autocomplete prompt",
                replaceSelection: "Default replace selection prompt",
                insertAtCursor: "Default insert at cursor prompt"
            }
        });
        await initializeAllSettings();
    });

    it('should update the model dropdown when switching providers', async () => {
        // Test the store logic for provider switching without component rendering

        // 1. Set Gemini model
        switchAIProvider('gemini');
        updateModelSelection('gemini-1.5-pro');

        // Verify Gemini is selected
        expect(get(appSettings).aiProvider).toBe('gemini');
        expect(get(appSettings).aiModel).toBe('gemini-1.5-pro');

        // 2. Switch to OpenAI
        switchAIProvider('openai');

        // 3. Verify OpenAI model is the default
        expect(get(appSettings).aiProvider).toBe('openai');
        expect(get(appSettings).aiModel).toBe('gpt-4o-mini');

        // 4. Select a different OpenAI model
        updateModelSelection('gpt-4o');
        expect(get(appSettings).aiModel).toBe('gpt-4o');

        // 5. Switch back to Gemini
        switchAIProvider('gemini');

        // 6. Verify Gemini model is restored
        expect(get(appSettings).aiProvider).toBe('gemini');
        expect(get(appSettings).aiModel).toBe('gemini-1.5-pro');
    });
});
