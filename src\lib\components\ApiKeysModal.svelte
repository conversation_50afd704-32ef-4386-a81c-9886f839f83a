<script lang="ts">
  import { apiKeys, updateApi<PERSON>ey } from '../stores/unifiedSettingsStore';

  // Component props
  let {
    isOpen = $bindable(false),
    onclose
  }: {
    isOpen?: boolean;
    onclose?: () => void;
  } = $props();

  let activeTab: 'gemini' | 'openai' | 'custom' = $state('openai');

  // Close modal and call callback
  const closeModal = () => {
    isOpen = false;
    onclose?.();
  };

  // Handle backdrop click to close
  const handleBackdropClick = (event: MouseEvent) => {
    if (event.target === event.currentTarget) {
      closeModal();
    }
  };

  // Handle escape key to close
  const handleKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      closeModal();
    }
  };

  // Auto-save API key changes
  const handleInput = (key: keyof typeof $apiKeys) => (event: Event) => {
    const target = event.target as HTMLInputElement;
    updateApiKey(key, target.value);
  };
</script>

<svelte:window onkeydown={handleKeydown} />

{#if isOpen}
  <!-- Modal backdrop with click-to-close -->
  <div
    class="modal-backdrop"
    role="dialog"
    aria-modal="true"
    tabindex="-1"
    onclick={handleBackdropClick}
    onkeydown={handleKeydown}
  >
    <div class="modal-content">
      <!-- Modal header -->
      <div class="modal-header">
        <h2>API Keys Configuration</h2>
        <button class="close-button" onclick={closeModal} aria-label="Close modal">
          ×
        </button>
      </div>

      <div class="modal-body">
        <!-- Provider tabs -->
        <div class="tabs">
          <button
            class="tab-button"
            class:active={activeTab === 'openai'}
            onclick={() => activeTab = 'openai'}
          >
            OpenAI
          </button>
          <button
            class="tab-button"
            class:active={activeTab === 'gemini'}
            onclick={() => activeTab = 'gemini'}
          >
            Gemini
          </button>
          <button
            class="tab-button"
            class:active={activeTab === 'custom'}
            onclick={() => activeTab = 'custom'}
          >
            Custom
          </button>
        </div>

        <!-- Tab content for each provider -->
        <div class="tab-content">
          {#if activeTab === 'openai'}
            <div class="provider-section">
              <h3>OpenAI Configuration</h3>
              <div class="input-group">
                <label for="openai-key">API Key:</label>
                <input
                  type="password"
                  id="openai-key"
                  placeholder="Enter your OpenAI API key"
                  bind:value={$apiKeys.openai}
                />
                <small>Get your API key from <a href="https://platform.openai.com/api-keys" target="_blank" rel="noopener noreferrer">OpenAI Platform</a></small>
              </div>
            </div>
          {/if}

          {#if activeTab === 'gemini'}
            <div class="provider-section">
              <h3>Google Gemini Configuration</h3>
              <div class="input-group">
                <label for="gemini-key">API Key:</label>
                <input
                  type="password"
                  id="gemini-key"
                  placeholder="Enter your Gemini API key"
                  bind:value={$apiKeys.gemini}
                />
                <small>Get your API key from <a href="https://aistudio.google.com/app/apikey" target="_blank" rel="noopener noreferrer">Google AI Studio</a></small>
              </div>
            </div>
          {/if}

          {#if activeTab === 'custom'}
            <div class="provider-section">
              <h3>Custom Provider Configuration</h3>
              <div class="input-group">
                <label for="custom-url">Base URL:</label>
                <input
                  type="text"
                  id="custom-url"
                  placeholder="e.g., https://openrouter.ai/api/v1 or https://api.example.com/v1"
                  bind:value={$apiKeys.customUrl}
                />
                <small>The base URL for your OpenAI-compatible API. For OpenRouter, use: <code>https://openrouter.ai/api/v1</code></small>
              </div>
              <div class="input-group">
                <label for="custom-key">API Key:</label>
                <input
                  type="password"
                  id="custom-key"
                  placeholder="Enter your custom API key"
                  bind:value={$apiKeys.customKey}
                />
                <small>The API key for your custom provider</small>
              </div>
            </div>
          {/if}
        </div>
      </div>

      <div class="modal-footer">
        <p class="auto-save-note">
          <span class="save-icon">💾</span>
          Keys are saved automatically as you type
        </p>
      </div>
    </div>
  </div>
{/if}

<style>
  .modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--color-modal-backdrop);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    transition: background-color 0.2s ease;
  }

  .modal-content {
    background: var(--color-modal-bg);
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 4px 20px var(--color-shadow);
    transition: all 0.2s ease;
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--color-border);
    background-color: var(--color-modal-header);
    transition: all 0.2s ease;
  }

  .modal-header h2 {
    margin: 0;
    color: var(--color-text-primary);
    font-size: 1.25rem;
  }

  .close-button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--color-text-primary);
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
  }

  .close-button:hover {
    background-color: var(--color-toggle-hover);
    color: var(--color-text-primary);
  }

  .modal-body {
    padding: 0;
  }

  .tabs {
    display: flex;
    border-bottom: 1px solid var(--color-border);
    background-color: var(--color-modal-header);
    transition: all 0.2s ease;
  }

  .tab-button {
    flex: 1;
    padding: 12px 16px;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 14px;
    color: var(--color-text-primary);
    opacity: 0.7;
    transition: all 0.2s;
    border-bottom: 2px solid transparent;
  }

  .tab-button:hover {
    background-color: var(--color-toggle-hover);
    opacity: 1;
  }

  .tab-button.active {
    color: var(--color-accent);
    border-bottom-color: var(--color-accent);
    background-color: var(--color-modal-bg);
    opacity: 1;
  }

  .tab-content {
    padding: 20px;
    min-height: 200px;
  }

  .provider-section h3 {
    margin: 0 0 20px 0;
    color: var(--color-text-primary);
    font-size: 1.1rem;
  }

  .input-group {
    margin-bottom: 20px;
  }

  .input-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: var(--color-text-primary);
    font-size: 14px;
  }

  .input-group input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--color-border);
    border-radius: 4px;
    font-size: 14px;
    background-color: var(--color-input-bg);
    color: var(--color-text-primary);
    transition: all 0.2s ease;
  }

  .input-group input:focus {
    outline: none;
    border-color: var(--color-accent);
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
  }

  .input-group small {
    display: block;
    margin-top: 6px;
    color: var(--color-text-primary);
    opacity: 0.7;
    font-size: 12px;
  }

  .input-group small a {
    color: var(--color-accent);
    text-decoration: none;
  }

  .input-group small a:hover {
    text-decoration: underline;
  }

  .input-group small code {
    background-color: var(--color-modal-header);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    color: var(--color-text-primary);
  }

  .modal-footer {
    padding: 16px 20px;
    border-top: 1px solid var(--color-border);
    background-color: var(--color-modal-header);
    transition: all 0.2s ease;
  }

  .auto-save-note {
    margin: 0;
    font-size: 12px;
    color: var(--color-text-primary);
    opacity: 0.7;
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .save-icon {
    font-size: 14px;
  }
</style>
